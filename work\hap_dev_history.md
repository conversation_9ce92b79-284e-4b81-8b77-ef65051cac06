# **项目提示词**

- # 纯鸿蒙开发提示词
- # Q:
<PERSON>，请开始HarmonyOS App开发：
1、完整阅读docs/*下文档，{prd|*architect|*ui|ux|*design}.md的内容非常重要，务必完整阅读并理解;
2、高保真原型在prototype/pages/*目录下，每一个html文件为一个单独的功能页，确保App的开发与高保真原型设计保持一致;
3、在app/*目录下完成编码，项目的框架我已经搭建完成，请不要破坏性的修复，确保符合HarmonyOS开发规范和最佳实践;
4、请先制详细的开发计划，再按计划一步一步执行，最后维护更新app/{PROJECT_SUMMARY|DEVELOPMENT_PROGRESS|README}.md文档
- # A:

- # Q:
James，请继续HarmonyOS App开发：
1、根据开发进行继续完成剩余所有页面编码任务
2、请先制详细的开发计划，再按计划一步一步执行，最后维护更新app/{PROJECT_SUMMARY|DEVELOPMENT_PROGRESS|README}.md文档
- # A:

- # Q:
James，请帮完成LLMs的ArkTS最佳实践编写：
1、根据app\arkts_rules.md TypeScript 到 ArkTS 的适配规则，整理大模型使用的ArkTS开发LLMs最佳实践，让大模型能输出符合ArkTS规范的代码，请确保LLMs的覆盖率和以及完整性
2、请先制详细的开发计划，再按计划一步一步执行，最后保存到app\llms_arkts_best_practices.md文档
- # A:

- # Q:
Quinn，请根据LLMs的ArkTS与ArkUI最佳实践与规范完成任务：
1. 执行任务前，确保正确的加载代理配置
2. 完整的识别项目app，使用规范对项目模块、架构、编码进行完整的评估
3. 找出项目中不符合规范的地方，并提供修改意见和建议
4. 将大任务拆解为小任务，将小任务分步骤执行，保存详细的执行方案到项目docs目录
5. 生产的文档保存到app/docs目录，按{编号}-{模块}-{名称}.md格式命名，文档顶部时间精确到分钟，时区为GMT+8 
6. 请先制详细的开发计划，再按计划一步一步执行
- # A:

- # 鸿蒙NEXT开发提示词v2
- # Q:
James，请开始HarmonyOS App开发任务：
1. 完整阅读docs/*下文档，{prd|*architect|*ui|ux|*design}.md的内容非常重要，务必完整阅读并理解;
2. 高保真原型在prototype/pages/*目录下，每一个html文件为一个单独的功能页;
3. 在app/*目录下完成编码，音乐播放核心功能框架我已经实现，请不要破坏性的修改，确保符合HarmonyOS开发LLMs的ArkTS与ArkUI最佳实践与规范;
4. 使用`color.json`进行暗色模式适配，通用主题色定义在`app\entry\src\main\resources\base\element\color.json`，暗色模式主题色定义在`app\entry\src\main\resources\dark\element\color.json`，页面使用`$r("app.color.xxx")`进行主题色的引用;
5. 根据高保真原型进行UI界面进行编码，最后完成核心播放功能与UI的集成，确保App编码与高保真原型设计保持一致;
6. 请先制详细的开发计划，再按计划一步一步执行，最后维护更新app/{PROJECT_SUMMARY|DEVELOPMENT_PROGRESS|README}.md文档
- # A: