---
type: "manual"
---

# HarmonyOS开发 - LLMs的ArkTS与ArkUI最佳实践与规范

## 概述

本文档为大语言模型(LLMs)提供HarmonyOS应用开发的标准化指导，基于ArkTS语言规范和HarmonyOS项目架构最佳实践。重点关注TypeScript到ArkTS的适配规则，确保AI生成的代码符合ArkTS规范要求。

**文档目标**：
- 为LLMs提供完整的ArkTS语法约束指导
- 确保生成的代码符合ArkTS编译要求
- 提供实用的代码模式和最佳实践
- 建立统一的代码质量标准

**适用范围**：
- 所有基于ArkTS的HarmonyOS应用开发
- LLMs辅助的代码生成和重构
- 代码审查和质量检查

## 核心架构原则

### 1. 严格分层架构 (CRITICAL)

**必须遵循的三层架构**:
```
Products层 (HAP包) → Features层 (HAR包) → Common层 (HAR包)
```

**依赖规则**:
- ✅ Products可以依赖Features和Common
- ✅ Features可以依赖Common和其他Features
- ❌ 绝对禁止反向依赖
- ❌ Common层不能依赖任何业务层

**实际应用**:
```typescript
// ✅ 正确: Products层引用Features
import { ComponentLibraryView } from '@ohos/componentlibrary';

// ✅ 正确: Features层引用Common
import { Logger, CommonConstants } from '@ohos/common';

// ❌ 错误: Common层引用Features
import { SampleCard } from '@ohos/devpractices'; // 禁止!
```

### 2. 模块包类型规范

**HAP包 (应用入口)**:
- 位置: `products/` 目录
- 用途: 设备特定的应用入口
- 配置: `"type": "entry"` in module.json5

**HAR包 (静态库)**:
- 位置: `features/` 和 `common/` 目录
- 用途: 可复用的业务模块和公共能力
- 配置: `"type": "har"` in module.json5

**设备类型配置**:
```json5
// 手机/平板/PC共包
"deviceTypes": ["phone", "tablet", "2in1"]
```

## ArkTS语法约束指导 (CRITICAL)

### 1. 强制静态类型约束

**禁止使用any和unknown类型**:
```typescript
// ❌ 错误: 使用any类型
let res: any = some_api_function('hello', 'world');

// ❌ 错误: 使用unknown类型
let value: unknown = getData();

// ✅ 正确: 使用具体类型
class CallResult {
  public succeeded(): boolean { return true; }
  public errorMessage(): string { return ''; }
}
let res: CallResult = some_api_function('hello', 'world');

// ✅ 正确: 使用Object作为通用类型
let value_o1: Object = true;
let value_o2: Object = 42;
```

**变量声明约束**:
```typescript
// ❌ 错误: 使用var关键字
var x = 'hello';

// ✅ 正确: 使用let声明变量
let x: string = 'hello';

// ✅ 正确: 使用const声明常量
const PI: number = 3.14159;
```

### 2. 对象和属性约束

**对象属性名必须是合法标识符**:
```typescript
// ❌ 错误: 数字或字符串作为属性名
var x = { name: 'x', 2: '3' };
console.info(x['name']);
console.info(x[2]);

// ✅ 正确: 使用类和数组
class X {
  public name: string = '';
}
let x: X = { name: 'x' };
console.info(x.name);

let y = ['a', 'b', 'c'];
console.info(y[2]);

// ✅ 正确: 使用Map处理非标识符key
let z = new Map<Object, string>();
z.set('name', '1');
z.set(2, '2');
```

**禁止运行时修改对象布局**:
```typescript
// ❌ 错误: 动态添加属性
class Point {
  public x: number = 0;
  public y: number = 0;
}
let p = new Point();
(p as any).z = 'Label'; // 编译错误

// ✅ 正确: 在类中预定义所有属性
class Point3D {
  public x: number = 0;
  public y: number = 0;
  public z: number = 0;
}
```

### 3. 函数和方法约束

**函数返回类型标注**:
```typescript
// ❌ 错误: 省略返回类型可能导致推断失败
function f(x: number) {
  if (x <= 0) {
    return x;
  }
  return g(x); // 如果g的返回类型未标注，会报错
}

// ✅ 正确: 显式标注返回类型
function f(x: number): number {
  if (x <= 0) {
    return x;
  }
  return g(x);
}

function g(x: number): number {
  return f(x - 1);
}
```

**禁止函数内声明函数**:
```typescript
// ❌ 错误: 函数内声明函数
function addNum(a: number, b: number): void {
  function logToConsole(message: string): void {
    console.info(message);
  }
  logToConsole('result');
}

// ✅ 正确: 使用lambda函数
function addNum(a: number, b: number): void {
  let logToConsole: (message: string) => void = (message: string): void => {
    console.info(message);
  };
  logToConsole('result');
}
```

### 4. 类型转换和断言

**使用as进行类型转换**:
```typescript
// ❌ 错误: 使用<type>语法
let c1 = <Circle>createShape();

// ✅ 正确: 使用as语法
let c2 = createShape() as Circle;

// ✅ 正确: 创建引用类型对象
let e2 = new Number(5.0) instanceof Number; // true
```

**限制一元运算符使用**:
```typescript
// ❌ 错误: 对字符串使用一元运算符
let b = +'5';    // 编译错误
let d = -'5';    // 编译错误
let f = ~'5';    // 编译错误

// ✅ 正确: 仅对数值类型使用
let a = +5;      // 正确
let c = -5;      // 正确
let e = ~5;      // 正确

// ✅ 正确: 显式类型转换
let b = Number.parseInt('5');
let d = -Number.parseInt('5');
```

### 5. 模块和导入约束

**导入语句位置**:
```typescript
// ❌ 错误: import语句不在文件开头
class C {
  s: string = '';
}
import foo from 'module1';

// ✅ 正确: import语句在文件开头
import foo from 'module1';
import * as bar from 'module2';

class C {
  s: string = '';
}
```

**禁止特定导入语法**:
```typescript
// ❌ 错误: 使用require语法
import m = require('mod');

// ❌ 错误: 使用export =语法
export = Point;

// ✅ 正确: 使用标准ES6导入导出
import * as m from 'mod';
export class Point { }
export default Point;
```

### 6. 接口和类约束

**接口继承限制**:
```typescript
// ❌ 错误: 接口继承类
class Control {
  state: number = 0;
}
interface SelectableControl extends Control {
  select(): void;
}

// ✅ 正确: 接口继承接口
interface Control {
  state: number;
}
interface SelectableControl extends Control {
  select(): void;
}
```

**类实现约束**:
```typescript
// ❌ 错误: 类implements类
class C {
  foo() {}
}
class C1 implements C {
  foo() {}
}

// ✅ 正确: 类implements接口
interface C {
  foo(): void;
}
class C1 implements C {
  foo() {}
}
```

## ArkTS类型系统最佳实践

### 1. 严格类型定义

**基础类型使用**:
```typescript
// ✅ 推荐的基础类型定义
let name: string = 'HarmonyOS';
let version: number = 4.0;
let isEnabled: boolean = true;
let items: string[] = ['item1', 'item2'];
let callback: (data: string) => void = (data) => console.info(data);
```

**对象类型定义**:
```typescript
// ❌ 错误: 使用对象字面量类型
let point: { x: number; y: number } = { x: 1, y: 2 };

// ✅ 正确: 使用类或接口定义
interface Point {
  x: number;
  y: number;
}
// 或者
class Point {
  x: number = 0;
  y: number = 0;
}
let point: Point = { x: 1, y: 2 };
```

### 2. 泛型使用规范

**泛型函数类型实参**:
```typescript
// ❌ 错误: 省略泛型类型实参可能导致推断失败
function greet<T>(): T {
  return 'Hello' as T;
}
let z = greet(); // 类型推断为unknown

// ✅ 正确: 显式提供泛型类型实参
let z = greet<string>();

// ✅ 正确: 可从参数推断的情况
function choose<T>(x: T, y: T): T {
  return Math.random() < 0.5 ? x : y;
}
let x = choose(10, 20); // 可以推断为number
```

**泛型约束**:
```typescript
// ❌ 错误: 使用条件类型
type X<T> = T extends number ? T : never;

// ✅ 正确: 使用显式约束
type X1<T extends number> = T;

// ✅ 正确: 使用Object重写
type X2<T> = Object;
```

### 3. 联合类型和可选属性

**联合类型使用**:
```typescript
// ✅ 正确: 基础联合类型
type Status = 'loading' | 'success' | 'error';
let currentStatus: Status = 'loading';

// ✅ 正确: 包含null的联合类型
let value: string | null = null;
let count: number | undefined = undefined;
```

**可选属性定义**:
```typescript
// ✅ 正确: 接口中的可选属性
interface UserConfig {
  name: string;
  age?: number;
  email?: string;
}

// ✅ 正确: 类中的可选属性处理
class User {
  name: string;
  age: number | undefined;
  email: string | undefined;

  constructor(config: UserConfig) {
    this.name = config.name;
    this.age = config.age;
    this.email = config.email;
  }
}
```

## ArkTS对象和类设计规范

### 1. 类定义最佳实践

**类字段声明**:
```typescript
// ❌ 错误: 在构造函数中声明字段
class Person {
  constructor(protected ssn: string, private firstName: string, private lastName: string) {
    this.ssn = ssn;
    this.firstName = firstName;
    this.lastName = lastName;
  }
}

// ✅ 正确: 在类作用域内显式声明字段
class Person {
  protected ssn: string;
  private firstName: string;
  private lastName: string;

  constructor(ssn: string, firstName: string, lastName: string) {
    this.ssn = ssn;
    this.firstName = firstName;
    this.lastName = lastName;
  }

  getFullName(): string {
    return this.firstName + ' ' + this.lastName;
  }
}
```

**私有字段使用**:
```typescript
// ❌ 错误: 使用#开头的私有字段
class C {
  #foo: number = 42;
}

// ✅ 正确: 使用private关键字
class C {
  private foo: number = 42;
}
```

**静态块限制**:
```typescript
// ❌ 错误: 多个静态块
class C {
  static s: string;

  static {
    C.s = 'aa';
  }
  static {
    C.s = C.s + 'bb';
  }
}

// ✅ 正确: 单个静态块
class C {
  static s: string;

  static {
    C.s = 'aa';
    C.s = C.s + 'bb';
  }
}
```

### 2. 对象字面量使用规范

**对象字面量类型标注**:
```typescript
// ❌ 错误: 未标注类型的对象字面量
let o1 = { n: 42, s: 'foo' };
let o2: Object = { n: 42, s: 'foo' };

// ✅ 正确: 显式类型标注
class C1 {
  n: number = 0;
  s: string = '';
}
let o1: C1 = { n: 42, s: 'foo' };
let o2: C1 = { n: 42, s: 'foo' };
```

**复杂对象初始化**:
```typescript
// ❌ 错误: 带方法的类使用字面量初始化
class C4 {
  n: number = 0;
  s: string = '';
  f() {
    console.info('Hello');
  }
}
let o7: C4 = { n: 42, s: 'foo', f: () => {} };

// ✅ 正确: 使用构造函数
let o7 = new C4();
o7.n = 42;
o7.s = 'foo';
```

### 3. 继承和接口设计

**接口继承规范**:
```typescript
// ✅ 正确: 接口继承接口
interface Identity {
  id: number;
  name: string;
}

interface Contact {
  email: string;
  phoneNumber: string;
}

interface Employee extends Identity, Contact {}
```

**类继承模式**:
```typescript
// ✅ 正确: 建立明确的继承关系
class B {
  n: number = 0;
  s: string = '';
}

class D extends B {
  constructor() {
    super();
  }
}

let b = new B();
let d = new D();
b = d; // 合法：子类赋值给父类
```

**避免structural typing**:
```typescript
// ❌ 错误: 依赖structural typing
class X {
  n: number = 0;
  s: string = '';
}

class Y {
  n: number = 0;
  s: string = '';
}

let x = new X();
let y = new Y();
y = x; // ArkTS中不允许

// ✅ 正确: 使用接口建立关系
interface Z {
  n: number;
  s: string;
}

class X implements Z {
  n: number = 0;
  s: string = '';
}

class Y implements Z {
  n: number = 0;
  s: string = '';
}

let x: Z = new X();
let y: Z = new Y();
y = x; // 合法：相同接口类型
```

### 4. 方法和属性访问

**属性访问方式**:
```typescript
// ❌ 错误: 通过索引访问属性
class Point {
  x: string = '';
  y: string = '';
}
let p: Point = { x: '1', y: '2' };
console.info(p['x']); // 编译错误

// ✅ 正确: 使用点操作符访问
console.info(p.x);
```

**方法重新赋值限制**:
```typescript
// ❌ 错误: 修改对象方法
class C {
  foo() {
    console.info('foo');
  }
}

function bar() {
  console.info('bar');
}

let c1 = new C();
c1.foo = bar; // 编译错误

// ✅ 正确: 使用继承扩展功能
class Derived extends C {
  foo() {
    console.info('Extra');
    super.foo();
  }
}

let c3 = new Derived();
c3.foo(); // Extra foo
```

### 1. 组件定义标准

**组件装饰器使用**:
```typescript
// ✅ 页面级组件
@Entry
@Component
struct MainPage {
  // 实现
}

// ✅ 可复用组件
@Component
export struct SampleCard {
  // 实现
}

// ✅ 性能优化组件
@Component({ freezeWhenInactive: true })
export struct PracticesView {
  // 实现
}
```

**状态管理模式**:
```typescript
// ✅ 全局状态
@StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = 
  AppStorage.get('GlobalInfoModel')!;

// ✅ 父子组件通信
@ObjectLink sampleCard: SampleCardData;
@Prop @Require sampleIndex: number;

// ✅ 跨组件状态
@Consume currentIndex: number;
@Provide('currentIndex') currentIndex: number = 0;
```

### 2. 响应式设计模式

**断点适配**:
```typescript
// ✅ 使用BreakpointType进行响应式设计
new BreakpointType({
  sm: CommonConstants.SPACE_16,
  md: CommonConstants.SPACE_16,
  lg: CommonConstants.SPACE_24,
}).getValue(this.globalInfoModel.currentBreakpoint)
```

**设备适配**:
```typescript
// ✅ 根据设备类型调整UI
this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
  Color.Transparent : $r('sys.color.comp_background_tertiary')
```


## ArkTS函数和方法编写规范

### 1. 函数定义约束

**函数表达式限制**:
```typescript
// ❌ 错误: 使用函数表达式
let f = function (s: string) {
  console.info(s);
};

// ✅ 正确: 使用箭头函数
let f = (s: string) => {
  console.info(s);
};

// ✅ 正确: 使用函数声明
function processData(s: string): void {
  console.info(s);
}
```

**参数解构限制**:
```typescript
// ❌ 错误: 参数解构
function drawText({ text = '', location: [x, y] = [0, 0], bold = false }) {
  // 处理逻辑
}

// ✅ 正确: 直接参数传递
function drawText(text: string, location: number[], bold: boolean): void {
  let x = location[0];
  let y = location[1];
  // 处理逻辑
}
```

**this使用限制**:
```typescript
// ❌ 错误: 在函数中使用this
function foo(i: string) {
  this.count = i; // 编译错误
}

// ✅ 正确: 在类的实例方法中使用this
class A {
  count: string = 'a';

  m(i: string): void {
    this.count = i;
  }
}
```

### 2. 异步函数和Promise

**异步函数定义**:
```typescript
// ✅ 正确: 异步函数返回类型标注
async function fetchData(): Promise<string> {
  try {
    const response = await fetch('/api/data');
    return await response.text();
  } catch (error) {
    throw new Error(`Failed to fetch data: ${error}`);
  }
}

// ✅ 正确: 异步方法在类中的使用
class DataService {
  async loadUserData(userId: number): Promise<UserData> {
    const data = await this.fetchFromServer(userId);
    return this.parseUserData(data);
  }

  private async fetchFromServer(id: number): Promise<string> {
    // 实现细节
    return '';
  }

  private parseUserData(data: string): UserData {
    // 解析逻辑
    return new UserData();
  }
}
```

### 3. 错误处理和异常

**异常抛出限制**:
```typescript
// ❌ 错误: 抛出非Error类型
throw 4;
throw '';
throw { message: 'error' };

// ✅ 正确: 抛出Error类或其派生类
throw new Error('Something went wrong');
throw new TypeError('Invalid type');

// ✅ 正确: 自定义错误类
class CustomError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CustomError';
  }
}
throw new CustomError('Custom error occurred');
```

**try-catch使用**:
```typescript
// ✅ 正确: 标准异常处理
try {
  const result = await riskyOperation();
  processResult(result);
} catch (error) {
  // 不需要类型标注
  const err = error as Error;
  Logger.error('Operation failed', err.message);

  // 用户友好的错误处理
  showErrorToast('操作失败，请重试');
}
```

### 4. 函数重载和泛型

**函数重载定义**:
```typescript
// ✅ 正确: 函数重载
function process(value: string): string;
function process(value: number): number;
function process(value: boolean): boolean;
function process(value: string | number | boolean): string | number | boolean {
  if (typeof value === 'string') {
    return value.toUpperCase();
  } else if (typeof value === 'number') {
    return value * 2;
  } else {
    return !value;
  }
}
```

**泛型函数约束**:
```typescript
// ✅ 正确: 带约束的泛型函数
function getProperty<T, K extends keyof T>(obj: T, key: K): T[K] {
  return obj[key];
}

// ✅ 正确: 多重泛型约束
function merge<T extends object, U extends object>(obj1: T, obj2: U): T & U {
  return { ...obj1, ...obj2 };
}
```

### 5. 高阶函数和回调

**回调函数类型定义**:
```typescript
// ✅ 正确: 明确的回调类型
type EventCallback = (event: string, data: Object) => void;
type DataProcessor<T> = (input: T) => T;

class EventManager {
  private callbacks: Map<string, EventCallback[]> = new Map();

  on(event: string, callback: EventCallback): void {
    if (!this.callbacks.has(event)) {
      this.callbacks.set(event, []);
    }
    this.callbacks.get(event)!.push(callback);
  }

  emit(event: string, data: Object): void {
    const eventCallbacks = this.callbacks.get(event);
    if (eventCallbacks) {
      eventCallbacks.forEach(callback => callback(event, data));
    }
  }
}
```

**数组处理函数**:
```typescript
// ✅ 正确: 类型安全的数组操作
function filterAndMap<T, U>(
  items: T[],
  predicate: (item: T) => boolean,
  mapper: (item: T) => U
): U[] {
  return items.filter(predicate).map(mapper);
}

// 使用示例
const numbers = [1, 2, 3, 4, 5];
const evenSquares = filterAndMap(
  numbers,
  (n) => n % 2 === 0,
  (n) => n * n
);
```

## 组件编码规范

### 1. 组件定义标准

**组件装饰器使用**:
```typescript
// ✅ 页面级组件
@Entry
@Component
struct MainPage {
  // 实现
}

// ✅ 可复用组件
@Component
export struct SampleCard {
  // 实现
}

// ✅ 性能优化组件
@Component({ freezeWhenInactive: true })
export struct PracticesView {
  // 实现
}
```

**状态管理模式**:
```typescript
// ✅ 全局状态
@StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel =
  AppStorage.get('GlobalInfoModel')!;

// ✅ 父子组件通信
@ObjectLink sampleCard: SampleCardData;
@Prop @Require sampleIndex: number;

// ✅ 跨组件状态
@Consume currentIndex: number;
@Provide('currentIndex') currentIndex: number = 0;
```

### 2. 响应式设计模式

**断点适配**:
```typescript
// ✅ 使用BreakpointType进行响应式设计
new BreakpointType({
  sm: CommonConstants.SPACE_16,
  md: CommonConstants.SPACE_16,
  lg: CommonConstants.SPACE_24,
}).getValue(this.globalInfoModel.currentBreakpoint)
```

**设备适配**:
```typescript
// ✅ 根据设备类型调整UI
this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
  Color.Transparent : $r('sys.color.comp_background_tertiary')
```

### 3. 路由管理规范

**路由定义**:
```typescript
// ✅ 使用NavPathStack进行路由管理
export class PageContext implements IPageContext {
  private readonly pathStack: NavPathStack;

  public openPage(data: RouterParam, animated: boolean = true): void {
    this.pathStack.pushPath({
      name: data.routerName,
      param: data.param,
    }, animated);
  }
}
```

**路由配置**:
```json
// router_map.json
{
  "routerMap": [
    {
      "name": "ComponentDetailView",
      "pageSourceFile": "src/main/ets/view/ComponentDetailView.ets",
      "buildFunction": "ComponentDetailBuilder"
    }
  ]
}
```
 
## 模块系统和导入导出规范

### 1. 导入语句规范

**导入语句位置和顺序**:
```typescript
// ✅ 正确: 所有import语句在文件开头
import { hilog } from '@kit.PerformanceAnalysisKit';
import { BusinessError } from '@kit.BasicServicesKit';
import * as fs from '@ohos.file.fs';
import { CommonConstants } from '@ohos/common';
import { Logger } from '../util/Logger';

// 类和其他声明
class DataProcessor {
  // 实现
}

// ❌ 错误: import语句不在开头
class SomeClass {
  // 实现
}
import { SomeModule } from 'module'; // 编译错误
```

**标准导入语法**:
```typescript
// ✅ 正确: 标准ES6导入语法
import { ComponentA, ComponentB } from '@ohos/componentlibrary';
import * as Utils from '@ohos/common';
import DefaultExport from './DefaultModule';

// ❌ 错误: require语法
import m = require('mod');

// ❌ 错误: CommonJS风格
const { ComponentA } = require('@ohos/componentlibrary');
```

### 2. 导出语句规范

**标准导出语法**:
```typescript
// ✅ 正确: 命名导出
export class UserService {
  // 实现
}

export interface UserData {
  id: number;
  name: string;
}

export const API_VERSION = '1.0';

// ✅ 正确: 默认导出
export default class MainComponent {
  // 实现
}

// ❌ 错误: export = 语法
export = UserService;
```

**Index.ets导出模式**:
```typescript
// ✅ 正确: 统一导出入口
// Index.ets
export { UserService } from './src/main/ets/service/UserService';
export { UserData, UserConfig } from './src/main/ets/model/UserData';
export { UserComponent } from './src/main/ets/component/UserComponent';
export { default as MainView } from './src/main/ets/view/MainView';

// 类型导出
export type { UserCallback } from './src/main/ets/types/UserTypes';
```

### 3. 模块依赖管理

**HAR包依赖配置**:
```json5
// oh-package.json5
{
  "name": "@ohos/usermodule",
  "version": "1.0.0",
  "main": "Index.ets",
  "license": "Apache-2.0",
  "dependencies": {
    "@ohos/common": "file:../../common",
    "@ohos/componentlibrary": "file:../componentlibrary"
  },
  "devDependencies": {
    "@ohos/hypium": "1.0.16"
  }
}
```

**模块间依赖规则**:
```typescript
// ✅ 正确: Features层依赖Common层
// features/usermodule/src/main/ets/service/UserService.ets
import { Logger, HttpClient } from '@ohos/common';
import { StorageManager } from '@ohos/common';

// ✅ 正确: Features层相互依赖
import { SharedComponent } from '@ohos/componentlibrary';

// ❌ 错误: Common层依赖Features层
// common/src/main/ets/util/SomeUtil.ets
import { UserService } from '@ohos/usermodule'; // 违反架构原则
```

### 4. 动态导入

**动态导入使用**:
```typescript
// ✅ 正确: 动态导入语法
async function loadModule(): Promise<void> {
  try {
    const module = await import('./OptionalModule');
    module.initialize();
  } catch (error) {
    Logger.error('Failed to load module', error);
  }
}

// ✅ 正确: 条件动态导入
class FeatureManager {
  async loadFeature(featureName: string): Promise<boolean> {
    try {
      switch (featureName) {
        case 'advanced':
          const advancedModule = await import('./AdvancedFeature');
          advancedModule.setup();
          return true;
        case 'premium':
          const premiumModule = await import('./PremiumFeature');
          premiumModule.activate();
          return true;
        default:
          return false;
      }
    } catch (error) {
      Logger.error(`Failed to load feature: ${featureName}`, error);
      return false;
    }
  }
}
```

### 5. 命名空间使用

**命名空间定义**:
```typescript
// ✅ 正确: 命名空间声明
namespace UserConstants {
  export const MAX_USERNAME_LENGTH = 50;
  export const MIN_PASSWORD_LENGTH = 8;
  export const DEFAULT_AVATAR = '/assets/default-avatar.png';

  export function validateUsername(username: string): boolean {
    return username.length <= MAX_USERNAME_LENGTH && username.length > 0;
  }
}

// ✅ 正确: 命名空间使用
const isValid = UserConstants.validateUsername('john_doe');
const maxLength = UserConstants.MAX_USERNAME_LENGTH;
```

**命名空间限制**:
```typescript
// ❌ 错误: 命名空间中的非声明语句
namespace A {
  export let x: number;
  x = 1; // 编译错误
}

// ✅ 正确: 使用函数包装非声明语句
namespace A {
  export let x: number;

  export function init(): void {
    x = 1;
  }
}

// 调用初始化
A.init();
```

### 6. 类型导入导出

**类型专用导入导出**:
```typescript
// ✅ 正确: 类型导入
import type { UserConfig, ApiResponse } from './types/UserTypes';
import type { ComponentProps } from '@ohos/componentlibrary';

// ✅ 正确: 类型导出
export type UserCallback = (user: UserData) => void;
export type { UserConfig } from './model/UserConfig';

// ✅ 正确: 混合导入
import { UserService, type UserData } from './UserModule';
```

**接口和类型定义文件**:
```typescript
// types/UserTypes.ets
export interface UserData {
  id: number;
  name: string;
  email: string;
  avatar?: string;
}

export interface UserConfig {
  theme: 'light' | 'dark';
  language: string;
  notifications: boolean;
}

export type UserRole = 'admin' | 'user' | 'guest';

export type UserCallback = (user: UserData) => void;
export type UserValidator = (user: Partial<UserData>) => boolean;
```

## 文件组织规范

### 1. 目录结构标准

**Features模块结构**:
```
features/[module-name]/
├── src/main/ets/
│   ├── component/          # 模块特定组件
│   ├── view/              # 页面视图
│   ├── viewmodel/         # 状态管理和业务逻辑
│   ├── service/           # 数据服务
│   ├── constant/          # 常量定义
│   └── model/             # 数据模型
├── src/main/resources/    # 模块资源
├── Index.ets             # 模块导出入口
├── module.json5          # 模块配置
├── oh-package.json5      # 依赖配置
└── build-profile.json5   # 构建配置
```

**Common层结构**:
```
common/
├── src/main/ets/
│   ├── component/         # 公共UI组件
│   ├── routermanager/     # 路由管理
│   ├── storagemanager/    # 存储管理
│   ├── util/              # 工具类
│   ├── viewmodel/         # 基础ViewModel
│   └── constant/          # 全局常量
└── Index.ets             # 公共能力导出
```

### 2. 命名约定

**文件命名**:
- 组件文件: `PascalCase.ets` (如: `SampleCard.ets`)
- 页面文件: `PascalCase.ets` (如: `MainPage.ets`)
- 工具类: `PascalCase.ets` (如: `Logger.ets`)
- 常量文件: `PascalCase.ets` (如: `CommonConstants.ets`)

**导出规范**:
```typescript
// ✅ Index.ets中统一导出
export { SampleCard } from './src/main/ets/component/SampleCard';
export { PracticesView } from './src/main/ets/view/PracticesView';
export { SampleDetailPageVM } from './src/main/ets/viewmodel/SampleDetailPageVM';
```

## 构建和配置规范

### 1. 包依赖管理

**oh-package.json5配置**:
```json5
{
  "name": "module-name",
  "version": "1.0.0",
  "main": "Index.ets",
  "license": "Apache-2.0",
  "dependencies": {
    "@ohos/common": "file:../../common",
    "@ohos/commonbusiness": "file:../commonbusiness"
  }
}
```

### 2. 构建配置

**module.json5标准**:
```json5
{
  "module": {
    "name": "module-name",
    "type": "har", // 或 "entry"
    "deviceTypes": ["default", "tablet", "2in1"],
    "routerMap": "$profile:router_map" // 如果有路由
  }
}
```

**混淆配置**:
```json5
// build-profile.json5
{
  "buildOptionSet": [
    {
      "name": "release",
      "arkOptions": {
        "obfuscation": {
          "ruleOptions": {
            "enable": true,
            "files": ["./obfuscation-rules.txt"]
          },
          "consumerFiles": ["./consumer-rules.txt"]
        }
      }
    }
  ]
}
```

## 性能优化专项指导

### 1. 组件性能优化

**组件懒加载和冻结**:
```typescript
// ✅ 使用freezeWhenInactive优化性能
@Component({ freezeWhenInactive: true })
export struct HeavyComponent {
  @State data: ComplexData[] = [];

  aboutToAppear(): void {
    // 组件激活时才加载数据
    this.loadData();
  }

  aboutToDisappear(): void {
    // 组件销毁时清理资源
    this.cleanup();
  }

  build() {
    // 复杂UI构建
  }
}

// ✅ 条件渲染优化
@Component
export struct ConditionalContent {
  @State showHeavyContent: boolean = false;

  build() {
    Column() {
      if (this.showHeavyContent) {
        // 仅在需要时渲染重型组件
        HeavyComponent()
      } else {
        // 轻量级占位符
        Text('点击加载内容')
          .onClick(() => {
            this.showHeavyContent = true;
          })
      }
    }
  }
}
```

**状态管理优化**:
```typescript
// ✅ 使用@ObjectLink减少不必要的重渲染
@Observed
class ListItemData {
  id: number = 0;
  title: string = '';
  isSelected: boolean = false;
}

@Component
export struct OptimizedListItem {
  @ObjectLink itemData: ListItemData;

  build() {
    Row() {
      Text(this.itemData.title)
        .fontColor(this.itemData.isSelected ? Color.Blue : Color.Black)
    }
    .backgroundColor(this.itemData.isSelected ? Color.Gray : Color.White)
  }
}

// ✅ 避免在build方法中创建对象
@Component
export struct PerformantComponent {
  @State items: string[] = [];
  private readonly itemHeight: number = 50; // 常量提取

  // ❌ 错误: 在build中创建对象
  // build() {
  //   List() {
  //     ForEach(this.items, (item: string) => {
  //       ListItem() {
  //         Text(item)
  //           .height(50) // 每次都创建新的数值
  //       }
  //     })
  //   }
  // }

  // ✅ 正确: 使用预定义常量
  build() {
    List() {
      ForEach(this.items, (item: string) => {
        ListItem() {
          Text(item)
            .height(this.itemHeight)
        }
      })
    }
  }
}
```

### 2. 内存管理优化

**对象池模式**:
```typescript
// ✅ 对象池减少GC压力
class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;

  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize: number = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;

    // 预创建对象
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }

  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }

  release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }
}

// 使用示例
class DataItem {
  id: number = 0;
  name: string = '';
  value: number = 0;
}

const dataItemPool = new ObjectPool<DataItem>(
  () => new DataItem(),
  (item) => {
    item.id = 0;
    item.name = '';
    item.value = 0;
  },
  20
);
```

**内存泄漏预防**:
```typescript
// ✅ 正确的资源管理
@Component
export struct ResourceManagedComponent {
  private timer: number | null = null;
  private subscription: Subscription | null = null;

  aboutToAppear(): void {
    // 设置定时器
    this.timer = setInterval(() => {
      this.updateData();
    }, 1000);

    // 订阅事件
    this.subscription = EventBus.subscribe('dataUpdate', this.handleDataUpdate.bind(this));
  }

  aboutToDisappear(): void {
    // 清理定时器
    if (this.timer !== null) {
      clearInterval(this.timer);
      this.timer = null;
    }

    // 取消订阅
    if (this.subscription !== null) {
      this.subscription.unsubscribe();
      this.subscription = null;
    }
  }

  private updateData(): void {
    // 更新逻辑
  }

  private handleDataUpdate(data: Object): void {
    // 处理数据更新
  }
}
```

### 3. 编译和构建优化

**Hvigor内存配置**:
```json5
// hvigor-config.json5
{
  "properties": {
    "hvigor.pool.cache.capacity": 0,
    "hvigor.pool.maxSize": 3,
    "ohos.arkCompile.maxSize": 2,
    "hvigor.enableMemoryCache": false,
    "hvigor.daemon": false
  }
}
```

**代码分割和懒加载**:
```typescript
// ✅ 动态导入实现代码分割
class FeatureLoader {
  private static loadedFeatures: Map<string, Object> = new Map();

  static async loadFeature(featureName: string): Promise<Object | null> {
    // 检查是否已加载
    if (this.loadedFeatures.has(featureName)) {
      return this.loadedFeatures.get(featureName)!;
    }

    try {
      let module: Object;

      switch (featureName) {
        case 'camera':
          module = await import('./features/CameraFeature');
          break;
        case 'location':
          module = await import('./features/LocationFeature');
          break;
        case 'payment':
          module = await import('./features/PaymentFeature');
          break;
        default:
          Logger.warn(`Unknown feature: ${featureName}`);
          return null;
      }

      this.loadedFeatures.set(featureName, module);
      return module;
    } catch (error) {
      Logger.error(`Failed to load feature ${featureName}`, error);
      return null;
    }
  }
}
```

### 4. 数据处理优化

**批量操作**:
```typescript
// ✅ 批量数据处理
class DataProcessor {
  private batchSize: number = 100;
  private processingQueue: DataItem[] = [];

  addItem(item: DataItem): void {
    this.processingQueue.push(item);

    if (this.processingQueue.length >= this.batchSize) {
      this.processBatch();
    }
  }

  private processBatch(): void {
    if (this.processingQueue.length === 0) return;

    const batch = this.processingQueue.splice(0, this.batchSize);

    // 批量处理，减少单次操作开销
    this.processItems(batch);
  }

  private processItems(items: DataItem[]): void {
    // 批量处理逻辑
    items.forEach(item => {
      // 处理单个项目
    });
  }

  flush(): void {
    // 处理剩余项目
    if (this.processingQueue.length > 0) {
      this.processBatch();
    }
  }
}
```

**缓存策略**:
```typescript
// ✅ LRU缓存实现
class LRUCache<K, V> {
  private capacity: number;
  private cache: Map<K, V> = new Map();

  constructor(capacity: number) {
    this.capacity = capacity;
  }

  get(key: K): V | undefined {
    if (this.cache.has(key)) {
      // 移动到最新位置
      const value = this.cache.get(key)!;
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return undefined;
  }

  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.capacity) {
      // 删除最旧的项目
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, value);
  }
}

// 使用示例
const imageCache = new LRUCache<string, ImageData>(50);
```

## 数据管理规范

### 1. ViewModel层架构模式

**ViewModel文件结构**:
```
features/[module]/src/main/ets/viewmodel/
├── [Module]State.ets          # 状态定义
├── [Module]ViewModel.ets       # 业务逻辑处理
└── [Module]PageVM.ets         # 页面级ViewModel (可选)
```

**State文件模式 (xxState.ets)**:
```typescript
// ✅ 状态类定义标准
@Observed
export class SampleDetailState extends BaseState {
  public sampleDatas: SampleDetailData[] = [];
  public sampleCount: number = 0;
  public loadingStatus: LoadingStatus = LoadingStatus.LOADING;
  public currentIndex: number = 0;
  public installingStatus: boolean = false;

  constructor() {
    super();
  }
}

// ✅ 数据模型定义
@Observed
export class SampleDetailData {
  public id: number = 0;
  public isFavorite: boolean = false;
  public mediaType: number = 0;
  public mediaUrl: string = '';
  public sampleCard: SampleCardData = new SampleCardData();
}
```

**ViewModel文件模式 (xxVM.ets)**:
```typescript
// ✅ ViewModel基础结构
export class SampleDetailPageVM extends BaseVM<SampleDetailState> {
  private static instance: SampleDetailPageVM;
  private model: SampleDetailModel = SampleDetailModel.getInstance();

  private constructor() {
    super(new SampleDetailState());
  }

  public static getInstance(): SampleDetailPageVM {
    if (!SampleDetailPageVM.instance) {
      SampleDetailPageVM.instance = new SampleDetailPageVM();
    }
    return SampleDetailPageVM.instance;
  }

  // 事件处理
  public sendEvent(event: BaseVMEvent): void {
    switch (event.eventType) {
      case SampleDetailEventType.LOAD_SAMPLE:
        this.loadSample();
        break;
      case SampleDetailEventType.DOWNLOAD_SAMPLE:
        this.downloadSample(event.data);
        break;
    }
  }
}
```

### 2. Model层架构模式

**Model文件结构**:
```
features/[module]/src/main/ets/model/
├── [Entity]Model.ets          # 业务模型 (单例，处理业务逻辑)
├── [Entity]Data.ets           # 数据结构定义
└── [Entity]Param.ets          # 参数传递对象
```

**业务模型文件 (xxModel.ets)**:
```typescript
// ✅ 业务模型标准结构
export class SampleModel {
  private service: SampleService = new SampleService();
  private static instance: SampleModel;

  private constructor() {}

  public static getInstance(): SampleModel {
    if (!SampleModel.instance) {
      SampleModel.instance = new SampleModel();
    }
    return SampleModel.instance;
  }

  // 业务方法
  public getSamplePage(currentPage: number, pageSize: number): Promise<ResponseData<SampleData>> {
    return this.service.getSamplePageByPreference(currentPage, pageSize)
      .then((data: ResponseData<SampleData>) => {
        return data;
      }).catch((err: string) => {
        Logger.error(TAG, `getSamplePage failed: ${err}`);
        return this.service.getSamplePage(currentPage, pageSize);
      });
  }
}
```

**数据结构文件 (xxData.ets)**:
```typescript
// ✅ 数据实体定义
export class SampleCardData {
  public id: number = 0;
  public cardTitle: string = '';
  public cardSubTitle: string = '';
  public cardType: CardTypeEnum = CardTypeEnum.UNKNOWN;
  public cardImage: string = '';
  public version: string = '';
  public sampleContents: SampleContent[] = [];
}

// ✅ 复杂数据结构
@Observed
export class SampleCategory {
  public loadingModel: LoadingModel = new LoadingModel();
  public currentPage: number = 1;
  public id: number = 0;
  public categoryName: string = '';
  public categoryType: number = 0;
  public sampleCards: SampleCardData[] = [];
}
```

**参数对象文件 (xxParam.ets)**:
```typescript
// ✅ 路由参数定义
export interface SampleDetailParams {
  currentIndex: number;
  sampleCardId: number;
}

export interface ComponentDetailParams {
  componentName: string;
  componentId: number;
}

// ✅ 事件参数定义
export interface CalculateHeightParam {
  yOffset: number;
  offset: number;
  state: ScrollState;
}

export interface OffsetParam {
  yOffset: number;
  tabIndex: number;
  breakpointChange?: boolean;
}
```

### 3. Service层架构模式

**Service文件结构**:
```
features/[module]/src/main/ets/service/
└── [Module]Service.ets        # 数据服务层
```

**Service实现模式**:
```typescript
// ✅ Service层标准结构
export class SampleService {
  public constructor() {}

  // Mock数据获取
  public getSampleListByMock(): Promise<ResponseData<SampleData>> {
    return new Promise((resolve, reject) => {
      MockRequest.call<ResponseData<SampleData>>(SampleTrigger.SAMPLE_PAGE)
        .then((result: Object) => {
          resolve(result as ResponseData<SampleData>);
        })
        .catch((error: BusinessError) => {
          reject(error);
        });
    });
  }

  // 网络请求
  public getSamplePage(currentPage?: number, pageSize?: number): Promise<ResponseData<SampleData>> {
    Logger.info(TAG, `getSamplePage param: currentPage ${currentPage}, pageSize: ${pageSize}`);
    return this.getSampleListByMock();
  }

  // 数据持久化
  public setSamplePageToPreference(data: ResponseData<SampleData>): void {
    PreferenceManager.getInstance().setValue(SampleTrigger.SAMPLE_PAGE, data);
  }
}
```

### 4. 常量和枚举定义

**常量文件结构**:
```
features/[module]/src/main/ets/constant/
└── [Module]Constants.ets      # 模块常量定义

common/src/main/ets/constant/
├── CommonConstants.ets        # 通用常量
└── CommonEnums.ets           # 通用枚举
```

**常量定义模式**:
```typescript
// ✅ 常量类定义
export class CommonConstants {
  // 尺寸常量
  public static readonly NAVIGATION_HEIGHT: number = 56;
  public static readonly TAB_BAR_HEIGHT: number = 48;
  public static readonly SPACE_8: number = 8;
  public static readonly SPACE_16: number = 16;

  // 百分比常量
  public static readonly FULL_PERCENT: string = '100%';

  // 功能常量
  public static readonly DYNAMIC_INSTALL_EVENT: string = 'DynamicInstallEvent';
  public static readonly PROMISE_WAIT = (delay: number) => new Promise<void>((resolve) => setTimeout(resolve, delay));
}

// ✅ 枚举定义
export enum LoadingStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  FAILED = 'failed',
  NO_NETWORK = 'no_network',
}

export enum ColumnEnum {
  SM = 4,
  MD = 8,
  LG = 12,
}
```

### 5. 工具类架构模式

**工具类文件结构**:
```
common/src/main/ets/util/
├── Logger.ets                # 日志工具
├── WindowUtil.ets            # 窗口工具
├── ImageUtil.ets             # 图片工具
├── BreakpointSystem.ets      # 断点系统
└── [Specific]Util.ets        # 特定功能工具
```

**工具类实现模式**:
```typescript
// ✅ 单例工具类
class Logger {
  private domain: number;
  private prefix: string;
  private format: string = '%{public}s, %{public}s';

  public constructor(prefix: string) {
    this.prefix = prefix;
    this.domain = 0xFF00;
  }

  public debug(...args: Object[]): void {
    hilog.debug(this.domain, this.prefix, this.format, args);
  }

  public info(...args: Object[]): void {
    hilog.info(this.domain, this.prefix, this.format, args);
  }

  public error(...args: Object[]): void {
    hilog.error(this.domain, this.prefix, this.format, args);
  }
}

export default new Logger('[HMOSWorld]');

// ✅ 静态工具类
export class ImageUtil {
  public static getColorFromImgUrl(imgUrl: string, isDeepColor?: boolean): Promise<number[]> {
    return new Promise((resolve, reject) => {
      ImageUtil.getColorDataByPreference(imgUrl).then((data: number[]) => {
        resolve(data);
      }).catch(() => {
        ImageUtil.getColorByPath(imgUrl, isDeepColor).then((colorData: number[]) => {
          resolve(colorData);
        }).catch((err: BusinessError) => {
          Logger.error(TAG, `Failed to get color data: ${err.message}`);
          reject(err);
        });
      });
    });
  }
}
```

### 6. 数据持久化

**PreferenceManager使用**:
```typescript
// ✅ 统一的存储管理
PreferenceManager.getInstance().setValue(key, value);
PreferenceManager.getInstance().getValue(key, defaultValue);
```

## 组件层架构模式

### 1. 组件文件结构

**组件目录组织**:
```
features/[module]/src/main/ets/component/
├── [Feature]Component.ets     # 功能组件
├── [UI]Card.ets              # 卡片组件
└── [Specific]View.ets        # 特定视图组件
```

**组件实现模式**:
```typescript
// ✅ 功能组件标准结构
@Component
export struct SampleComponent {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  @ObjectLink singleSampleData: SampleDetailData;
  @Prop @Require sampleIndex: number;
  @Prop showIndicator: boolean = false;

  build() {
    Column() {
      // 组件内容
      if (this.singleSampleData.mediaType === MediaTypeEnum.VIDEO) {
        VideoComponent({
          videoUrl: this.singleSampleData.mediaUrl,
          showIndicator: this.showIndicator
        })
      } else {
        SampleCard({
          sampleCard: this.singleSampleData.sampleCard,
          sampleIndex: this.sampleIndex
        })
      }
    }
    .width('100%')
    .height('100%')
  }
}

// ✅ 卡片组件模式
@Component
export struct SampleCard {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  @ObjectLink sampleCard: SampleCardData;
  @Prop sampleIndex: number;
  viewModel: SampleDetailPageVM = SampleDetailPageVM.getInstance();

  build() {
    Column() {
      // 卡片内容
      Text(this.sampleCard.title)
        .fontSize($r('sys.float.Body_L'))
        .fontColor($r('sys.color.font_emphasize'))

      Row() {
        Button($r('app.string.read_code'))
          .onClick(() => {
            this.viewModel.sendEvent(new BindSheetEvent(this.sampleIndex, true));
          })

        Button($r('app.string.download_sample'))
          .onClick(() => {
            this.viewModel.sendEvent(new LoadSampleEvent());
          })
      }
    }
    .borderRadius($r('sys.float.corner_radius_level8'))
    .backgroundColor($r('sys.color.comp_background_primary'))
  }
}
```

### 2. 页面视图模式

**页面文件结构**:
```
features/[module]/src/main/ets/view/
└── [Module]View.ets           # 页面视图

products/[device]/src/main/ets/page/
└── [Page]Page.ets            # 页面入口
```

**页面实现模式**:
```typescript
// ✅ 页面级组件
@Entry
@Component
struct MainPage {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  @State currentIndex: number = 0;
  private tabController: TabsController = new TabsController();

  build() {
    Tabs({ controller: this.tabController }) {
      TabContent() {
        ComponentLibraryView()
      }.tabBar(this.TabBuilder(TabBarType.HOME))

      TabContent() {
        PracticesView()
      }.tabBar(this.TabBuilder(TabBarType.SAMPLE))
    }
    .onChange((index: number) => {
      this.currentIndex = index;
    })
  }

  @Builder
  TabBuilder(tabType: TabBarType) {
    // Tab构建器实现
  }
}

// ✅ 功能视图组件
@Component({ freezeWhenInactive: true })
export struct PracticesView {
  viewModel: PracticeViewModel = PracticeViewModel.getInstance();
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  @State practiceState: PracticeState = this.viewModel.getState();

  aboutToAppear(): void {
    this.viewModel.sendEvent({
      type: PracticeEventType.LOAD_SAMPLE_PAGE,
      param: new LoadSamplePageParam()
    });
  }

  build() {
    BaseHomeView({
      bannerState: this.practiceState.bannerState,
      topNavigationData: this.practiceState.topNavigationData,
      contentView: () => {
        this.ContentBuilder()
      }
    })
  }

  @Builder
  ContentBuilder() {
    // 内容构建器
  }
}
```

## 网络和集成规范

### 1. 网络请求

**Web组件集成**:
```typescript
// ✅ Web内容加载
WebUtil.createWebNode(url, this.getUIContext(), NestedScrollMode.SELF_ONLY);

// ✅ 清理资源
aboutToDisappear(): void {
  WebUtil.removeNode(this.sampleCard.originalUrl);
}
```

**网络权限配置**:
```json
// hmos_web_config.json
{
  "whitelist": "{\"UrlPermissionList\":[{\"scheme\":\"https\",\"host\":\"developer.huawei.com\"}]}"
}
```

### 2. 动态模块管理

**Sample下载**:
```typescript
// ✅ 动态模块安装
DynamicInstallManager.fetchModule(ctx, moduleName)
  .then((data: moduleInstallManager.ModuleInstallSessionState) => {
    if (data.code === moduleInstallManager.RequestErrorCode.SUCCESS) {
      this.state.taskId = data.taskId;
    }
  });
```

## 错误处理和类型转换规范

### 1. 异常处理最佳实践

**异常类型限制**:
```typescript
// ❌ 错误: 抛出基础类型
throw 404;
throw 'Network error';
throw { code: 500, message: 'Server error' };

// ✅ 正确: 抛出Error类或其派生类
throw new Error('Network connection failed');
throw new TypeError('Expected string but got number');

// ✅ 正确: 自定义错误类
class NetworkError extends Error {
  public code: number;

  constructor(message: string, code: number) {
    super(message);
    this.name = 'NetworkError';
    this.code = code;
  }
}

throw new NetworkError('Connection timeout', 408);
```

**try-catch语句规范**:
```typescript
// ✅ 正确: 标准异常处理
async function fetchUserData(userId: number): Promise<UserData | null> {
  try {
    const response = await httpClient.get(`/users/${userId}`);
    return this.parseUserData(response.data);
  } catch (error) {
    // 不需要类型标注，直接使用
    const err = error as Error;
    Logger.error('Failed to fetch user data', err.message);

    // 根据错误类型进行不同处理
    if (err instanceof NetworkError) {
      showNetworkErrorDialog();
    } else if (err instanceof ValidationError) {
      showValidationErrorToast(err.message);
    } else {
      showGenericErrorToast('操作失败，请重试');
    }

    return null;
  }
}
```

### 2. 类型转换和类型保护

**安全类型转换**:
```typescript
// ✅ 正确: 使用as进行类型转换
class Shape {}
class Circle extends Shape {
  radius: number = 0;
}
class Square extends Shape {
  side: number = 0;
}

function processShape(shape: Shape): void {
  // 使用instanceof进行类型检查
  if (shape instanceof Circle) {
    const circle = shape as Circle;
    console.info(`Circle radius: ${circle.radius}`);
  } else if (shape instanceof Square) {
    const square = shape as Square;
    console.info(`Square side: ${square.side}`);
  }
}

// ❌ 错误: 不安全的类型转换
function unsafeConversion(shape: Shape): void {
  const circle = shape as Circle; // 可能运行时失败
  console.info(circle.radius);
}
```

**类型保护函数**:
```typescript
// ✅ 正确: 使用instanceof和as进行类型保护
function isCircle(shape: Object): boolean {
  return shape instanceof Circle;
}

function isSquare(shape: Object): boolean {
  return shape instanceof Square;
}

function processShapeSafely(shape: Object): void {
  if (isCircle(shape)) {
    const circle = shape as Circle;
    console.info(`Processing circle with radius: ${circle.radius}`);
  } else if (isSquare(shape)) {
    const square = shape as Square;
    console.info(`Processing square with side: ${square.side}`);
  } else {
    Logger.warn('Unknown shape type');
  }
}
```

### 3. 空值和未定义处理

**null和undefined处理**:
```typescript
// ✅ 正确: 明确的null检查
function processUserName(name: string | null | undefined): string {
  if (name === null || name === undefined) {
    return 'Anonymous';
  }

  if (name.trim().length === 0) {
    return 'Empty Name';
  }

  return name.trim();
}

// ✅ 正确: 可选链操作
interface User {
  profile?: {
    avatar?: string;
    settings?: {
      theme: string;
    };
  };
}

function getUserTheme(user: User): string {
  return user.profile?.settings?.theme ?? 'default';
}
```

**数组和对象安全访问**:
```typescript
// ✅ 正确: 安全的数组访问
function getFirstItem<T>(items: T[]): T | undefined {
  return items.length > 0 ? items[0] : undefined;
}

function getItemAt<T>(items: T[], index: number): T | undefined {
  if (index >= 0 && index < items.length) {
    return items[index];
  }
  return undefined;
}

// ✅ 正确: 安全的对象属性访问
function getProperty<T, K extends keyof T>(obj: T, key: K): T[K] | undefined {
  return obj && obj[key] !== undefined ? obj[key] : undefined;
}
```

### 4. 业务错误处理模式

**结果包装模式**:
```typescript
// ✅ 正确: 结果包装类
class Result<T> {
  private constructor(
    private readonly success: boolean,
    private readonly data?: T,
    private readonly error?: Error
  ) {}

  static ok<T>(data: T): Result<T> {
    return new Result(true, data);
  }

  static fail<T>(error: Error): Result<T> {
    return new Result(false, undefined, error);
  }

  isSuccess(): boolean {
    return this.success;
  }

  getData(): T | undefined {
    return this.data;
  }

  getError(): Error | undefined {
    return this.error;
  }
}

// 使用示例
async function fetchUserSafely(userId: number): Promise<Result<UserData>> {
  try {
    const userData = await userService.getUser(userId);
    return Result.ok(userData);
  } catch (error) {
    return Result.fail(error as Error);
  }
}
```

**错误码枚举**:
```typescript
// ✅ 正确: 错误码定义
export enum ErrorCode {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}

export class BusinessError extends Error {
  constructor(
    public readonly code: ErrorCode,
    message: string,
    public readonly details?: Object
  ) {
    super(message);
    this.name = 'BusinessError';
  }
}

// 使用示例
function validateUser(userData: UserData): void {
  if (!userData.email) {
    throw new BusinessError(
      ErrorCode.VALIDATION_ERROR,
      'Email is required',
      { field: 'email' }
    );
  }
}
```

### 5. 调试和日志记录

**调试信息记录**:
```typescript
// ✅ 正确: 结构化日志记录
class DebugLogger {
  static logMethodEntry(className: string, methodName: string, params?: Object): void {
    Logger.debug(`[${className}] Entering ${methodName}`, params);
  }

  static logMethodExit(className: string, methodName: string, result?: Object): void {
    Logger.debug(`[${className}] Exiting ${methodName}`, result);
  }

  static logError(className: string, methodName: string, error: Error): void {
    Logger.error(`[${className}] Error in ${methodName}`, {
      message: error.message,
      stack: error.stack
    });
  }
}

// 使用示例
class UserService {
  async createUser(userData: UserData): Promise<UserData> {
    DebugLogger.logMethodEntry('UserService', 'createUser', userData);

    try {
      const result = await this.saveUser(userData);
      DebugLogger.logMethodExit('UserService', 'createUser', result);
      return result;
    } catch (error) {
      DebugLogger.logError('UserService', 'createUser', error as Error);
      throw error;
    }
  }
}
```

## 错误处理和日志

### 1. 统一日志管理

**Logger使用**:
```typescript
// ✅ 统一日志格式
const TAG = '[ComponentName]';
Logger.info(TAG, `Operation completed: ${result}`);
Logger.error(TAG, `Operation failed: ${error.message}`);
```

### 2. 错误处理模式

**异常捕获**:
```typescript
// ✅ 完整的错误处理
try {
  const result = await someAsyncOperation();
  Logger.info(TAG, `Success: ${result}`);
} catch (error) {
  const businessError = error as BusinessError;
  Logger.error(TAG, `Error ${businessError.code}: ${businessError.message}`);
  // 用户友好的错误提示
  promptAction.showToast({
    message: '操作失败，请重试',
    duration: 2000
  });
}
```

## 测试规范

### 1. 单元测试

**测试文件组织**:
```
src/ohosTest/ets/
├── test/
│   ├── ComponentTest.ets
│   ├── ViewModelTest.ets
│   └── ServiceTest.ets
└── TestRunner.ets
```

**测试编写模式**:
```typescript
// ✅ 标准测试结构
import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';

export default function ComponentTest() {
  describe('ComponentTest', () => {
    beforeEach(() => {
      // 测试前准备
    });

    it('should render correctly', () => {
      // 测试实现
      expect(result).assertEqual(expected);
    });
  });
}
```

## LLMs代码生成检查清单

### 1. ArkTS语法合规性检查

**基础语法检查**:
- [ ] 使用`let`/`const`而非`var`声明变量
- [ ] 所有变量都有明确的类型标注或可推断类型
- [ ] 禁止使用`any`、`unknown`类型
- [ ] 函数返回类型明确标注（特别是递归调用场景）
- [ ] 使用箭头函数而非函数表达式
- [ ] 类字段在类作用域内显式声明，不在构造函数中声明

**对象和类检查**:
- [ ] 对象属性名为合法标识符，不使用数字或字符串
- [ ] 对象字面量有明确的类型标注
- [ ] 类使用`private`关键字而非`#`声明私有字段
- [ ] 接口继承接口，类实现接口，避免structural typing
- [ ] 不在运行时修改对象布局（添加/删除属性）

**函数和方法检查**:
- [ ] 不在函数内声明函数，使用lambda函数替代
- [ ] 不使用参数解构，直接传递参数
- [ ] `this`仅在类的实例方法中使用
- [ ] 异常抛出仅使用Error类或其派生类

### 2. 类型系统检查

**类型定义检查**:
- [ ] 泛型函数有明确的类型实参或可从参数推断
- [ ] 联合类型使用明确的类型而非条件类型
- [ ] 接口和类型别名命名唯一，不与其他标识符冲突
- [ ] 枚举成员类型一致，不混合不同类型

**类型转换检查**:
- [ ] 使用`as`语法进行类型转换，不使用`<type>`语法
- [ ] 类型保护使用`instanceof`和`as`组合
- [ ] 一元运算符仅用于数值类型
- [ ] 避免隐式类型转换，使用显式转换方法

### 3. 模块系统检查

**导入导出检查**:
- [ ] 所有`import`语句在文件开头
- [ ] 使用标准ES6导入语法，不使用`require`
- [ ] 使用标准`export`语法，不使用`export =`
- [ ] Index.ets文件正确导出模块内容
- [ ] 模块依赖关系符合三层架构原则

**命名空间检查**:
- [ ] 命名空间仅包含声明，非声明语句在函数中
- [ ] 命名空间不被用作对象
- [ ] 动态导入语法正确，有适当的错误处理

### 4. HarmonyOS特定检查

**组件定义检查**:
- [ ] 组件使用正确的装饰器（@Component、@Entry等）
- [ ] 状态管理使用正确的装饰器（@State、@Prop、@ObjectLink等）
- [ ] 组件性能优化（freezeWhenInactive等）
- [ ] 响应式设计使用BreakpointType

**架构合规检查**:
- [ ] 文件组织符合标准目录结构
- [ ] 模块类型配置正确（HAP/HAR）
- [ ] 依赖关系符合分层架构原则
- [ ] 路由配置正确（router_map.json）

### 5. 性能和资源检查

**性能优化检查**:
- [ ] 重型组件使用懒加载和条件渲染
- [ ] 避免在build方法中创建对象
- [ ] 正确使用@ObjectLink减少重渲染
- [ ] 实现适当的资源清理（aboutToDisappear）

**内存管理检查**:
- [ ] 定时器和订阅在组件销毁时清理
- [ ] 大数据集使用分页或虚拟化
- [ ] 缓存策略合理，避免内存泄漏
- [ ] 对象池模式用于频繁创建的对象

### 6. 错误处理检查

**异常处理检查**:
- [ ] try-catch语句不标注catch参数类型
- [ ] 异常处理覆盖所有可能的错误场景
- [ ] 错误信息对用户友好
- [ ] 日志记录包含足够的调试信息

**类型安全检查**:
- [ ] null和undefined检查完整
- [ ] 数组和对象访问有边界检查
- [ ] 类型转换有运行时验证
- [ ] 可选属性正确处理

### 7. 代码质量检查

**命名规范检查**:
- [ ] 文件命名使用PascalCase（.ets文件）
- [ ] 类名、接口名使用PascalCase
- [ ] 变量名、方法名使用camelCase
- [ ] 常量使用UPPER_SNAKE_CASE

**代码结构检查**:
- [ ] 单一职责原则，类和方法功能明确
- [ ] 依赖注入而非硬编码依赖
- [ ] 配置外部化，不在代码中硬编码
- [ ] 适当的注释和文档

### 8. 测试覆盖检查

**测试完整性检查**:
- [ ] 关键业务逻辑有单元测试
- [ ] 组件渲染有测试覆盖
- [ ] 错误场景有测试用例
- [ ] 性能关键路径有性能测试

## 代码审查检查清单

### 必检项目

**架构合规性**:
- [ ] 是否遵循三层架构
- [ ] 依赖方向是否正确
- [ ] 模块类型配置是否正确

**代码质量**:
- [ ] 组件是否正确使用装饰器
- [ ] 状态管理是否合理
- [ ] 错误处理是否完整
- [ ] 日志记录是否规范

**性能考虑**:
- [ ] 是否使用了适当的性能优化
- [ ] 内存使用是否合理
- [ ] 响应式设计是否正确实现

**安全性**:
- [ ] 网络请求是否在白名单内
- [ ] 用户输入是否经过验证
- [ ] 敏感信息是否正确处理

## 常见问题和解决方案

### 1. 构建问题

**内存不足**:
```bash
# 解决方案: 调整hvigor内存配置
"hvigor.pool.maxSize": 3
"ohos.arkCompile.maxSize": 2
```

**依赖冲突**:
```bash
# 解决方案: 检查oh-package.json5依赖版本
# 确保所有模块使用相同的公共依赖版本
```

### 2. 运行时问题

**路由失败**:
```typescript
// 解决方案: 检查router_map.json配置
// 确保buildFunction名称与实际导出一致
```

**状态同步问题**:
```typescript
// 解决方案: 使用@Watch监听状态变化
@StorageProp('GlobalInfoModel') @Watch('handleStateChange')
globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
```

这份规范确保了所有LLMs在HarmonyOS开发中保持一致的代码风格、架构模式和最佳实践。
