<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>听汀音乐(TingTing Music) - 原型预览</title>
  <!-- CDN引入 -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
  <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
  <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
  <script src="components/iphone-frame.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            // 根据项目配色进行更新
            primary: {
              light: '#4A90E2',
              DEFAULT: '#2D4A6B',
              dark: '#1A1D23',
              bg: '#F5F7FA',
            },
          },
        },
      },
      darkMode: 'class',
    };
  </script>
  <style>
    /** 以下配色信息根据项目配色进行更新 **/
    body {
      font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      background-color: #f5f5f7;
      padding: 20px 10px;
      color: #333;
      overflow-x: hidden;
      -webkit-font-smoothing: antialiased;
      transition: background-color 0.3s, color 0.3s;
    }

    .dark body {
      background-color: #1e1e1e;
      color: #f5f5f7;
    }

    .project-title {
      background: linear-gradient(135deg, #4A90E2, #2D4A6B);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .flow-container {
      display: flex;
      overflow-x: auto;
      padding: 1rem 0;
      gap: 1rem;
    }

    .flow-item {
      min-width: 250px;
      flex-shrink: 0;
    }

    .flow-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 40px;
      flex-shrink: 0;
    }

    .step-number {
      width: 22px;
      height: 22px;
      background-color: #4A90E2;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      margin-right: 10px;
      flex-shrink: 0;
    }

    .preview-label {
      margin-bottom: 1rem;
      transition: all 0.2s ease;
    }

    .preview-item:hover .preview-label {
      transform: translateY(-3px);
    }

    /* 暗黑模式切换开关 */
    .theme-toggle {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 50;
      background-color: white;
      border-radius: 20px;
      padding: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      transition: background-color 0.3s;
    }

    .dark .theme-toggle {
      background-color: #2d3748;
    }

    .toggle-track {
      width: 50px;
      height: 24px;
      background-color: #e2e8f0;
      border-radius: 12px;
      position: relative;
      transition: background-color 0.3s;
      cursor: pointer;
    }

    .dark .toggle-track {
      background-color: #4a5568;
    }

    .toggle-thumb {
      width: 20px;
      height: 20px;
      background-color: white;
      border-radius: 50%;
      position: absolute;
      top: 2px;
      left: 2px;
      transition: transform 0.3s, background-color 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .dark .toggle-thumb {
      transform: translateX(26px);
      background-color: #1a202c;
    }
  </style>
</head>

<body x-data="appData()" x-init="init()">
  <!-- 暗黑模式切换开关 -->
  <div class="theme-toggle">
    <div class="toggle-track" id="themeToggle" @click="toggleDarkMode()">
      <div class="toggle-thumb">
        <iconify-icon icon="ph:sun" class="text-yellow-500 dark:hidden" width="14"></iconify-icon>
        <iconify-icon icon="ph:moon" class="text-blue-300 hidden dark:block" width="14"></iconify-icon>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto">
    <!-- 头部 -->
    <div class="text-center mb-8">
      <h1 class="project-title text-4xl font-bold mb-2">听汀音乐(TingTing Music)</h1>
      <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto mb-2">
        数字时代的私人音乐港湾 - 专注于纯粹本地音乐播放的美学产品
      </p>
      <p class="text-sm text-primary-light dark:text-primary-light max-w-2xl mx-auto">
        <span class="font-medium">最新特性:</span> 水墨汀岸设计 | 完全离线运行 | 锚点收藏系统
      </p>
    </div>

    <!-- 用户流程图 -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 mb-8 shadow-lg">
      <div class="flex items-center justify-between text-xl font-semibold mb-6 text-gray-800 dark:text-gray-200">
        <div class="flex items-center">
          <iconify-icon icon="fluent:flow-16-filled" class="text-primary mr-3" width="24"></iconify-icon>
          核心功能流程
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- 流程1：音乐播放 -->
        <div class="bg-gray-50 dark:bg-gray-900 rounded-xl p-5">
          <div class="flex items-center text-lg font-semibold mb-4 text-primary">
            <iconify-icon icon="fluent:music-note-2-16-filled" class="mr-2" width="22"></iconify-icon>
            <span>音乐播放</span>
          </div>

          <div class="space-y-3">
            <template x-for="item in taskFlowItems" :key="item.step">
              <div class="flex items-center bg-white dark:bg-gray-800 rounded-lg p-3 hover:shadow-md transition-shadow">
                <div
                  class="flex items-center justify-center w-8 h-8 bg-primary rounded-full text-white text-sm font-semibold mr-3"
                  x-text="item.step"></div>
                <div class="flex-grow font-medium text-gray-700 dark:text-gray-300" x-text="item.title"></div>
                <iconify-icon icon="fluent:arrow-right-16-filled" class="text-gray-400" width="16"></iconify-icon>
              </div>
            </template>
          </div>
        </div>

        <!-- 流程2：音乐库管理 -->
        <div class="bg-gray-50 dark:bg-gray-900 rounded-xl p-5">
          <div class="flex items-center text-lg font-semibold mb-4 text-primary">
            <iconify-icon icon="fluent:library-24-filled" class="mr-2" width="22"></iconify-icon>
            <span>音乐库管理</span>
          </div>

          <div class="space-y-3">
            <template x-for="item in dataFlowItems" :key="item.step">
              <div class="flex items-center bg-white dark:bg-gray-800 rounded-lg p-3 hover:shadow-md transition-shadow">
                <div
                  class="flex items-center justify-center w-8 h-8 bg-primary rounded-full text-white text-sm font-semibold mr-3"
                  x-text="item.step"></div>
                <div class="flex-grow font-medium text-gray-700 dark:text-gray-300" x-text="item.title"></div>
                <iconify-icon icon="fluent:arrow-right-16-filled" class="text-gray-400" width="16"></iconify-icon>
              </div>
            </template>
          </div>
        </div>

        <!-- 流程3：播放列表 -->
        <div class="bg-gray-50 dark:bg-gray-900 rounded-xl p-5">
          <div class="flex items-center text-lg font-semibold mb-4 text-primary">
            <iconify-icon icon="fluent:list-24-filled" class="mr-2" width="22"></iconify-icon>
            <span>播放列表</span>
          </div>

          <div class="space-y-3">
            <template x-for="item in userFlowItems" :key="item.step">
              <div class="flex items-center bg-white dark:bg-gray-800 rounded-lg p-3 hover:shadow-md transition-shadow">
                <div
                  class="flex items-center justify-center w-8 h-8 bg-primary rounded-full text-white text-sm font-semibold mr-3"
                  x-text="item.step"></div>
                <div class="flex-grow font-medium text-gray-700 dark:text-gray-300" x-text="item.title"></div>
                <iconify-icon icon="fluent:arrow-right-16-filled" class="text-gray-400" width="16"></iconify-icon>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增：核心功能亮点 -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 mb-8 shadow-lg">
      <div class="flex items-center justify-between text-xl font-semibold mb-6 text-gray-800 dark:text-gray-200">
        <div class="flex items-center">
          <iconify-icon icon="fluent:sparkle-16-filled" class="text-primary mr-3" width="24"></iconify-icon>
          核心功能亮点
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- 使用Alpine.js循环渲染功能亮点 -->
        <template x-for="feature in featureHighlights" :key="feature.title">
          <div class="bg-gray-50 dark:bg-gray-900 rounded-xl p-5 transition-transform hover:scale-105">
            <div class="flex items-center text-lg font-semibold mb-4 text-primary">
              <iconify-icon :icon="feature.icon" class="mr-2" width="22"></iconify-icon>
              <span x-text="feature.title"></span>
            </div>
            <p class="text-gray-600 dark:text-gray-400 text-sm" x-text="feature.description"></p>
          </div>
        </template>
      </div>
    </div>

    <!-- 最新更新 -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 mb-8 shadow-lg">
      <div class="flex items-center justify-between text-xl font-semibold mb-6 text-gray-800 dark:text-gray-200">
        <div class="flex items-center">
          <iconify-icon icon="fluent:history-24-filled" class="text-primary mr-3" width="24"></iconify-icon>
          最近更新
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 使用Alpine.js循环渲染更新内容 -->
        <template x-for="update in recentUpdates" :key="update.title">
          <div class="bg-gray-50 dark:bg-gray-900 rounded-xl p-5">
            <div class="flex items-center text-lg font-semibold mb-4 text-primary">
              <iconify-icon :icon="update.icon" class="mr-2" width="22"></iconify-icon>
              <span x-text="update.title"></span>
            </div>
            <ul class="text-gray-600 dark:text-gray-400 text-sm space-y-2">
              <template x-for="(item, index) in update.items" :key="index">
                <li class="flex items-start">
                  <span class="text-primary mr-2">•</span>
                  <span x-text="item"></span>
                </li>
              </template>
            </ul>
          </div>
        </template>
      </div>
    </div>

    <!-- 布局控制 -->
    <div class="flex justify-center mb-6">

      <!-- 设备选项控制 -->
      <div class="flex flex-wrap justify-center gap-3 my-4">
        <!-- 布局样式选择 -->
        <div class="inline-flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1 mb-2">
          <button @click="setLayoutType('standard')"
            :class="{'bg-primary text-white': layout.type === 'standard', 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700': layout.type !== 'standard'}"
            class="py-1 px-3 rounded-md text-sm font-medium">
            标准视图
          </button>
          <button @click="setLayoutType('compact')"
            :class="{'bg-primary text-white': layout.type === 'compact', 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700': layout.type !== 'compact'}"
            class="py-1 px-3 rounded-md text-sm font-medium">
            紧凑视图
          </button>
        </div>

        <!-- 设备型号选择 -->
        <div class="inline-flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1 mb-2">
          <select @change="setDeviceModel($event.target.value)"
            class="py-1 px-3 rounded-md bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm font-medium border-none focus:ring-0">
            <option value="iphone15pro">iPhone 15 Pro</option>
            <option value="iphone13">iPhone 13</option>
            <option value="iphonese">iPhone SE</option>
            <option value="ipad">iPad</option>
          </select>
        </div>

        <!-- 状态栏样式选择 -->
        <div class="inline-flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1 mb-2">
          <button @click="setStatusBarStyle('auto')"
            :class="{'bg-primary text-white': layout.statusBarStyle === 'auto', 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700': layout.statusBarStyle !== 'auto'}"
            class="py-1 px-3 rounded-md text-sm font-medium">
            自动
          </button>
          <button @click="setStatusBarStyle('light')"
            :class="{'bg-primary text-white': layout.statusBarStyle === 'light', 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700': layout.statusBarStyle !== 'light'}"
            class="py-1 px-3 rounded-md text-sm font-medium">
            浅色
          </button>
          <button @click="setStatusBarStyle('dark')"
            :class="{'bg-primary text-white': layout.statusBarStyle === 'dark', 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700': layout.statusBarStyle !== 'dark'}"
            class="py-1 px-3 rounded-md text-sm font-medium">
            深色
          </button>
        </div>
      </div>
    </div>

    <!-- 应用页面预览 -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 mb-8 shadow-lg">
      <!-- 应用页面标题 -->
      <div class="flex items-center justify-between text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
        <div class="flex items-center">
          <iconify-icon icon="fluent:picture-in-picture-20-filled" class="text-primary mr-3" width="24"></iconify-icon>
          核心页面
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="preview-grid">
        <!-- 使用Alpine.js循环渲染核心页面 - 请勿修改删除，添加页面在页面尾部配置Alpine.js渲染数据 #Alpine.js 数据管理# -->
        <template x-for="page in corePages" :key="page.url">
          <div class="flex flex-col items-center">
            <a :href="page.url" target="_blank"
              class="preview-label py-2 px-4 rounded-lg bg-gradient-to-r from-orange-50 to-orange-100 dark:from-gray-800 dark:to-gray-700 shadow-sm border border-orange-100 dark:border-orange-900">
              <span x-text="page.title"></span>
            </a>
            <iphone-frame :content-url="page.url" show-status-bar="true" scale="0.5"></iphone-frame>
          </div>
        </template>
      </div>
    </div>

    <!-- 其他二级页面 -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 mb-8 shadow-lg">
      <!-- 预览网格标题 -->
      <div class="flex items-center justify-between text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
        <div class="flex items-center">
          <iconify-icon icon="fluent:layer-20-filled" class="text-primary mr-3" width="24"></iconify-icon>
          功能与设置页面
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8" id="settings-grid">
        <!-- 使用Alpine.js循环渲染设置页面 -->
        <template x-for="page in settingPages" :key="page.url">
          <div class="flex flex-col items-center">
            <a :href="page.url" target="_blank"
              class="preview-label py-2 px-4 rounded-lg bg-gradient-to-r from-orange-50 to-orange-100 dark:from-gray-800 dark:to-gray-700 shadow-sm border border-orange-100 dark:border-orange-900">
              <span x-text="page.title"></span>
            </a>
            <iphone-frame :content-url="page.url" scale="0.5"></iphone-frame>
          </div>
        </template>
      </div>
    </div>

    <!-- 页脚 - 请据项目信息更新-->
    <div class="text-center text-gray-500 dark:text-gray-400 py-6">
      <p>© 2025 听汀音乐(TingTing Music) | 数字时代的私人音乐港湾</p>
    </div>
  </div>

  <script>
    // #Alpine.js 数据管理 - 要添加的项目信息根据类型在此函数内声明
    function appData() {
      return {
        // 核心页面数据 - 以下内容为示例，请据项目信息更新
        corePages: [
          { title: "主页面", "url": "pages/main.html" },
          { title: '搜索', url: 'pages/search.html' },
          { title: '音乐库', url: 'pages/library.html' },
          { title: '我的', url: 'pages/profile.html' },
          { title: '播放器', url: 'pages/player.html' },
        ],

        // 功能与设置页面数据 - 以下内容为示例，请据项目信息更新
        settingPages: [
          { title: "专辑", "url": "pages/album.html" },
          { title: "我的锚点", "url": "pages/anchors.html" },
          { title: "艺术家", "url": "pages/artist.html" },
          { title: "空汀状态", "url": "pages/empty-state.html" },
          { title: "均衡器", "url": "pages/equalizer.html" },
          { title: "音乐空间", "url": "pages/music-space.html" },
          { title: "播放器", "url": "pages/player.html" },
          { title: "播放列表", "url": "pages/playlist.html" },
          { title: "个人资料", "url": "pages/profile.html" },
          { title: "系统设置", "url": "pages/settings.html" },
          { title: "歌曲详情", "url": "pages/song-detail.html" },
          { title: "歌曲列表", "url": "pages/song-list.html" },
          { title: "视觉测试", "url": "pages/visual-test.html" },
          { title: "桌面组件", "url": "pages/desktop-widget.html" },
          { title: "注册", "url": "pages/register.html" },
          { title: "登录", "url": "pages/login.html" },
          { title: "数据备份", "url": "pages/backup.html" },
          { title: "隐私设置", "url": "pages/privacy-settings.html" },
          { title: "用户协议", "url": "pages/user-agreement.html" },
          { title: "帮助反馈", "url": "pages/help.html" },
        ],

        // {任务管理}流程数据 - 请据项目信息更新 示例 { step: 1, title: '创建任务' }
        taskFlowItems: [],

        // {数据分析}流程数据 - 请据项目信息更新 示例 { step: 1, title: '查看统计' }
        dataFlowItems: [],

        // {用户中心}流程数据 - 请据项目信息更新 示例 { step: 1, title: '个性化设置' }
        userFlowItems: [],

        // {核心功能亮点}数据 - 请据项目信息更新
        featureHighlights: [],

        // {最近更新}数据 - 请据项目信息更新
        recentUpdates: [],

        // 布局控制
        layout: {
          type: 'standard', // 'standard' 或 'compact'
          deviceModel: 'iphone15pro',
          statusBarStyle: 'auto' // 自动 auto、浅色 light、深色 dark
        },

        // 切换布局类型
        setLayoutType(type) {
          this.layout.type = type;
          const previewGrid = document.getElementById('preview-grid');
          const settingsGrid = document.getElementById('settings-grid');
          const frames = document.querySelectorAll('iphone-frame');

          if (type === 'standard') {
            previewGrid.classList.remove('gap-2');
            previewGrid.classList.add('gap-6');

            settingsGrid.classList.remove('gap-2');
            settingsGrid.classList.add('gap-6');

            frames.forEach(frame => {
              frame.setAttribute('scale', '0.5');
            });
          } else {
            previewGrid.classList.remove('gap-6');
            previewGrid.classList.add('gap-2');

            settingsGrid.classList.remove('gap-6');
            settingsGrid.classList.add('gap-2');

            frames.forEach(frame => {
              frame.setAttribute('scale', '0.4');
            });
          }
        },

        // 设置设备型号
        setDeviceModel(model) {
          this.layout.deviceModel = model;
          const frames = document.querySelectorAll('iphone-frame');
          const grids = document.querySelectorAll('#preview-grid, #settings-grid');

          frames.forEach(frame => {
            frame.setAttribute('model', model);
          });

          if (model === 'ipad') {
            grids.forEach(grid => {
              grid.className = grid.className.replace('xl:grid-cols-4', 'xl:grid-cols-3');
              grid.className = grid.className.replace('lg:grid-cols-3', 'lg:grid-cols-2');
            });
          } else {
            grids.forEach(grid => {
              grid.className = grid.className.replace('xl:grid-cols-3', 'xl:grid-cols-4');
              grid.className = grid.className.replace('lg:grid-cols-2', 'lg:grid-cols-3');
            });
          }
        },

        // 设置状态栏样式
        setStatusBarStyle(style) {
          this.layout.statusBarStyle = style;
          const frames = document.querySelectorAll('iphone-frame');

          frames.forEach(frame => {
            frame.setAttribute('status-bar-style', style);
          });
        },

        // 切换暗黑模式
        toggleDarkMode() {
          const htmlElement = document.documentElement;

          if (htmlElement.classList.contains('dark')) {
            htmlElement.classList.remove('dark');
            localStorage.setItem('theme', 'light');
          } else {
            htmlElement.classList.add('dark');
            localStorage.setItem('theme', 'dark');
          }

          // 刷新所有iframe内容以更新深色模式
          setTimeout(() => {
            const frames = document.querySelectorAll('iphone-frame');
            frames.forEach(frame => {
              try {
                const iframe = frame.shadowRoot.querySelector('iframe');
                if (iframe && iframe.contentWindow) {
                  iframe.contentWindow.location.reload();
                }
              } catch (e) {
                console.error('Failed to reload iframe:', e);
              }
            });
          }, 300);
        },

        // 初始化应用
        init() {
          // 检查本地存储中的主题偏好
          if (localStorage.getItem('theme') === 'dark' ||
            (!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }

          // 设置默认状态栏样式
          this.setStatusBarStyle('auto');
        }
      };
    }
  </script>
</body>

</html>